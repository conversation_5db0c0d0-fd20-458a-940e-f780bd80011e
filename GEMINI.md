- You can find general information about the product, tech, coding style guide and architecture inside `./ai-assets/docs` folder. You MUST always take a look there for context before you do any serious amount of work.

- Depending on the kind of task you're doing, be it front or back-end, you can find specialized guides in the `./ai-assets/docs` folder (front-end-guide.md, back-end-guide.md) - they go deeper on the specific areas. Otherwise `./ai-assets/docs/style-guide.md` contains the general style guide which should always be valid.

- The `./ai-assets/docs/repository.md` file contains general information about the current nx monorepo and high level apps and libs overview.

- If the user asks you to use "feature planning" as part of your workflow you must take a look at `./ai-assets/docs/feature-planning.md` which contains instructions on how to plan features and strictly follow.

- If the user asks you to open a pull request for them - make sure within the description to note that this was opened by gemini-cli as a request from {the current user}. You're the one drafting the description. The base branch is always "main" unless explicitly stated otherwise.
