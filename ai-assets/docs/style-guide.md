### General

We're trying to use the strictest types possible everywhere. Any usage of `any` or type casting in general should be a last resort, accompanied by a comment to justify why we need it.

We use a bunch of nominal type helpers imported from `@mio/helpers` - do reach for them and find if they're suitable. Usually they're tightly coupled to `zod` parsers. We do use `zod` a lot to parse (and type transform) data coming from the outside world into our app code, e.g. - when reading an entity from the database in the backend or when receiving data on the front end from an api or when submitting a front end form and validating user's input.

We prefer `type` over `interface` always.
We prefer `const someFunc = ()` over `function someFunc()` always.
If a function's parameters become more than 2 - consider refactoring them into a single object.

### Error handling

We have special error classes like `UnexpectedError`, `DomainError` and `ParsingError` that we try to consistently use everywhere. They are exported from `@mio/helpers`. We have the convention to now throw errors but return either one of those errors or a result, similar to how people use an `Either` in functional programming, we're just not using any special data type wrapper like they do. We have helpers like `isError` to then fork our logic depending on the result.

Each custom error class supports additional context which can be useful - do try to provide it, either via `error.addContext()` or as an additional argument to its constructor.

Be careful to not overdo logging and make it spammy.
