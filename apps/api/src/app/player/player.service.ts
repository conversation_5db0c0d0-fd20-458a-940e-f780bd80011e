import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  AssignTeamDto,
  ChangeStatusDto,
  CreatePlayerDto,
  DomainError,
  ErrorMessages,
  GetPlayerDto,
  isError,
  Pagination,
  ParsingError,
  Player,
  PlayerId,
  PlayersSearchParams,
  PlayerTeamProfileId,
  PopulatedPlayer,
  ProfileId,
  TeamId,
  UnexpectedError,
  ApplyToOrganizationDto,
  Email,
  PlayerUser,
  OrganizationId,
  PositiveInteger,
  Assets,
  CustomError,
  PlayerTeamStatusWithTeam,
  PaymentRequest,
  CustomDate,
  PlayerTeamProfile,
  UUID,
  PlayerTeamStatus,
} from "@mio/helpers";

import { PlayerRepository } from "./player.repository";
import { PlayerTeamProfileService } from "../player-team-profile";
import { AssetsSharedService } from "../assets/assets-shared.service";
import { PaymentRequestsService } from "../payments/payment-requests.service";
import { TeamService } from "../team/team.service";

@Injectable()
export class PlayerService {
  constructor(
    private repo: PlayerRepository,
    private playerTeamProfileService: PlayerTeamProfileService,
    private assetsService: AssetsSharedService,
    private paymentRequestsService: PaymentRequestsService,
    private teamsService: TeamService,
  ) {}

  async applyToOrganization(dto: ApplyToOrganizationDto) {
    const existingPlayer = await this.repo.getById(dto.playerId);

    if (isError(existingPlayer)) {
      existingPlayer.addContext({
        service: PlayerService.name,
        method: this.applyToOrganization.name,
        operation: this.repo.getById.name,
        playerId: dto.playerId,
        organizationId: dto.organizationId,
        message: "Couldn't get player",
      });

      return existingPlayer;
    }

    if (isNil(existingPlayer)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.applyToOrganization.name,
        operation: this.repo.getById.name,
        playerId: dto.playerId,
        message: "Player not found",
        organizationId: dto.organizationId,
      });
    }

    const assignResult = await this.playerTeamProfileService.assignOrganization({
      organizationId: dto.organizationId,
      playerId: dto.playerId,
      firstName: existingPlayer.firstName,
      lastName: existingPlayer.lastName,
      email: existingPlayer.email,
      phone: existingPlayer.phone,
      dob: existingPlayer.dob,
      medicalConditions: existingPlayer.medicalConditions,
      gender: existingPlayer.gender,
      playingExperience: existingPlayer.playingExperience,
      playingExperienceDescription: existingPlayer.playingExperienceDescription,
      address: existingPlayer.address,
    });

    return assignResult;
  }

  // TODO: run in transaction
  async createApplicant(dto: CreatePlayerDto) {
    const findPlayerDto = Player.parseGetPlayerDto({
      email: dto.email,
      firstName: dto.firstName,
      lastName: dto.lastName,
      guardianEmail: dto.guardian?.email,
    });

    if (isError(findPlayerDto)) {
      return new UnexpectedError(findPlayerDto, {
        service: PlayerService.name,
        method: this.createApplicant.name,
        operation: Player.parseGetPlayerDto.name,
        playerEmail: dto.email,
        dto,
        message: "Failed to parse player dto",
      });
    }

    const existingPlayer = await this.findExistingPlayer(findPlayerDto);

    if (isError(existingPlayer)) {
      existingPlayer.addContext({
        service: PlayerService.name,
        method: this.createApplicant.name,
        operation: this.findExistingPlayer.name,
        playerEmail: dto.email,
        message: "Failed to find existing player",
      });

      return existingPlayer;
    }

    if (existingPlayer) {
      /* there is a separate method for existing players applications */
      return new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: PlayerService.name,
        method: this.createApplicant.name,
        operation: this.findExistingPlayer.name,
        playerEmail: dto.email,
        message: "Player already exists",
      });
    }

    const player = Player.createNewPlayer(dto);

    const playerSaveResult = await this.repo.createPlayer(player);

    if (isError(playerSaveResult)) {
      playerSaveResult.addContext({
        service: PlayerService.name,
        method: this.createApplicant.name,
        operation: this.repo.createPlayer.name,
        playerId: player.id,
      });

      return playerSaveResult;
    }

    await this.playerTeamProfileService.assignOrganization({
      organizationId: dto.organizationId,
      playerId: player.id,
      firstName: player.firstName,
      lastName: player.lastName,
      email: player.email,
      phone: player.phone,
      dob: player.dob,
      medicalConditions: player.medicalConditions,
      address: player.address,
      gender: player.gender,
      playingExperience: player.playingExperience,
      playingExperienceDescription: player.playingExperienceDescription,
    });

    return player;
  }

  async demoBulkAssignTeam(
    playerIds: PlayerId[],
    teamId: TeamId,
    organizationId: OrganizationId,
    profileId: ProfileId,
  ) {
    for (let index = 0; index < playerIds.length; index++) {
      const assigned = await this.assignNewTeam(
        { teamId, organizationId, playerId: playerIds[index] },
        profileId,
      );

      if (isError(assigned)) {
        assigned.addContext({
          service: PlayerService.name,
          method: this.demoBulkAssignTeam.name,
          operation: this.assignNewTeam.name,
          playerId: playerIds[index],
        });

        return assigned;
      }
    }

    return undefined;
  }

  async markDocumentsAsSubmitted(playerId: PlayerId): Promise<CustomError | Player> {
    const player = await this.repo.getById(playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.markDocumentsAsSubmitted.name,
        operation: this.repo.getById.name,
        playerId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.markDocumentsAsSubmitted.name,
        operation: this.repo.getById.name,
        playerId,
      });
    }

    const updatedPlayer = Player.markDocumentsAsSubmitted(player);

    if (isError(updatedPlayer)) {
      updatedPlayer.addContext({
        service: PlayerService.name,
        method: this.markDocumentsAsSubmitted.name,
        operation: Player.markDocumentsAsSubmitted.name,
        playerId,
      });

      return updatedPlayer;
    }

    const updateResult = await this.repo.updatePlayer(updatedPlayer);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: PlayerService.name,
        method: this.markDocumentsAsSubmitted.name,
        operation: this.repo.updatePlayer.name,
        playerId,
      });

      return updateResult;
    }

    return updatedPlayer;
  }

  async addPhoto(playerId: PlayerId, photoId: Assets.Image.ImageId): Promise<CustomError | Player> {
    const player = await this.repo.getById(playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.addPhoto.name,
        operation: this.repo.getById.name,
        playerId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.addPhoto.name,
        operation: this.repo.getById.name,
        playerId,
      });
    }

    const updatedPlayer = Player.addPhoto(player, photoId);

    if (isError(updatedPlayer)) {
      updatedPlayer.addContext({
        service: PlayerService.name,
        method: this.addPhoto.name,
        operation: Player.addPhoto.name,
        playerId,
      });

      return updatedPlayer;
    }

    const updateResult = await this.repo.updatePlayer(updatedPlayer);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: PlayerService.name,
        method: this.addPhoto.name,
        operation: this.repo.updatePlayer.name,
        playerId,
      });
      return updateResult;
    }

    return updatedPlayer;
  }

  async removePhoto(playerId: PlayerId): Promise<CustomError | Player> {
    const player = await this.repo.getById(playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.removePhoto.name,
        operation: this.repo.getById.name,
        playerId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.removePhoto.name,
        operation: this.repo.getById.name,
        playerId,
      });
    }

    const updatedPlayer = Player.removePhoto(player);

    if (isError(updatedPlayer)) {
      updatedPlayer.addContext({
        service: PlayerService.name,
        method: this.removePhoto.name,
        operation: Player.removePhoto.name,
        playerId,
      });

      return updatedPlayer;
    }

    const updateResult = await this.repo.updatePlayer(updatedPlayer);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: PlayerService.name,
        method: this.removePhoto.name,
        operation: this.repo.updatePlayer.name,
        playerId,
      });

      return updateResult;
    }

    return updatedPlayer;
  }

  async addImageDocument(
    playerId: PlayerId,
    documentId: Assets.Image.ImageId,
  ): Promise<CustomError | Player> {
    const player = await this.repo.getById(playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.addImageDocument.name,
        operation: this.repo.getById.name,
        playerId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.addImageDocument.name,
        operation: this.repo.getById.name,
        playerId,
      });
    }

    const updatedPlayer = Player.addImageDocument(player, documentId);

    if (isError(updatedPlayer)) {
      updatedPlayer.addContext({
        service: PlayerService.name,
        method: this.addImageDocument.name,
        operation: Player.addImageDocument.name,
        playerId,
      });

      return updatedPlayer;
    }

    const updateResult = await this.repo.updatePlayer(updatedPlayer);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: PlayerService.name,
        method: this.addImageDocument.name,
        operation: this.repo.updatePlayer.name,
        playerId,
      });

      return updateResult;
    }

    return updatedPlayer;
  }

  async removeImageDocument(
    playerId: PlayerId,
    photoId: Assets.Image.ImageId,
  ): Promise<CustomError | Player> {
    const player = await this.repo.getById(playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.removeImageDocument.name,
        operation: this.repo.getById.name,
        playerId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.removeImageDocument.name,
        operation: this.repo.getById.name,
        playerId,
      });
    }

    const updatedPlayer = Player.removeImageDocument(player, photoId);

    if (isError(updatedPlayer)) {
      updatedPlayer.addContext({
        service: PlayerService.name,
        method: this.removeImageDocument.name,
        operation: Player.removeImageDocument.name,
        playerId,
      });

      return updatedPlayer;
    }

    const updateResult = await this.repo.updatePlayer(updatedPlayer);

    if (isError(updateResult)) {
      updateResult.addContext({
        service: PlayerService.name,
        method: this.removeImageDocument.name,
        operation: this.repo.updatePlayer.name,
        playerId,
      });

      return updateResult;
    }

    return updatedPlayer;
  }

  async getPlayerImageDocuments(
    playerId: PlayerId,
  ): Promise<Assets.Image.ImageAsset[] | CustomError> {
    const player = await this.repo.getById(playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.getPlayerImageDocuments.name,
        operation: this.repo.getById.name,
        playerId,
      });

      return player;
    }

    if (!player) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.getPlayerImageDocuments.name,
        operation: this.repo.getById.name,
        playerId,
      });
    }

    if (!player.documents?.image_document_ids) {
      return [];
    }

    const images = await this.assetsService.getImagesByIds(player.documents?.image_document_ids);

    if (isError(images)) {
      images.addContext({
        service: PlayerService.name,
        method: this.getPlayerImageDocuments.name,
        operation: this.assetsService.getImagesByIds.name,
        player,
      });

      return images;
    }

    return images;
  }

  async updatePlayer(dto: Player, playerUser: PlayerUser.PlayerUser) {
    // TODO: replace with a GUARD
    if (
      playerUser.authentication.email !== dto.email &&
      playerUser.authentication.email !== dto.guardian?.email
    ) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const player = await this.repo.getById(dto.id);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.updatePlayer.name,
        operation: this.repo.getById.name,
        playerId: dto.id,
        playerUserId: playerUser.id,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedPlayer = Player.updatePlayer(dto, player);

    const updateResult = await this.repo.updatePlayer(updatedPlayer);

    if (isError(updateResult)) {
      return new UnexpectedError(updateResult);
    }

    const email = updatedPlayer.email || updatedPlayer.guardian?.email;

    if (!email) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const phone = updatedPlayer.phone || updatedPlayer.guardian?.phone;

    if (!phone) {
      return new DomainError(ErrorMessages.InvalidAction);
    }

    const profile = PlayerTeamProfile.createApplicant({
      playerId: updatedPlayer.id,
      // here it doesn't matter what organizationId is, because we are not saving it to the database
      // this is a hack but I don't want to create a separate entity method just for this
      organizationId: UUID.generate<OrganizationId>(),

      firstName: updatedPlayer.firstName,
      lastName: updatedPlayer.lastName,
      email,
      phone,
      dob: updatedPlayer.dob,
      medicalConditions: updatedPlayer.medicalConditions,
      gender: updatedPlayer.gender,

      playingExperience: updatedPlayer.playingExperience,
      playingExperienceDescription: updatedPlayer.playingExperienceDescription,
      address: updatedPlayer.address,
      guardian: updatedPlayer.guardian,
      documents: updatedPlayer.documents,
    });

    await this.playerTeamProfileService.updatePersonalInfo(profile);

    return updatedPlayer;
  }

  async assignNewTeam(
    dto: AssignTeamDto,
    modifierId: ProfileId,
  ): Promise<UnexpectedError | DomainError | ParsingError | PopulatedPlayer> {
    const player = await this.repo.getById(dto.playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.assignNewTeam.name,
        operation: this.repo.getById.name,
        playerId: dto.playerId,
        coachUserId: modifierId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedProfile = await this.playerTeamProfileService.assignNewTeam({
      ...dto,
      modifierId,
    });

    if (isError(updatedProfile)) {
      updatedProfile.addContext({
        service: PlayerService.name,
        method: this.assignNewTeam.name,
        operation: this.playerTeamProfileService.assignNewTeam.name,
        playerId: dto.playerId,
        coachUserId: modifierId,
      });

      return updatedProfile;
    }

    return Player.toPopulatedPlayer(player, [updatedProfile]);
  }

  async softRemoveFromTeam(
    dto: ChangeStatusDto,
    modifierId: ProfileId,
  ): Promise<UnexpectedError | DomainError | ParsingError | PopulatedPlayer> {
    return this.changeStatus(dto, modifierId);
  }

  async changeStatus(
    dto: ChangeStatusDto,
    modifierId: ProfileId,
  ): Promise<UnexpectedError | DomainError | ParsingError | PopulatedPlayer> {
    const player = await this.repo.getById(dto.playerId);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.changeStatus.name,
        operation: this.repo.getById.name,
        playerId: dto.playerId,
        coachUserId: modifierId,
      });

      return player;
    }

    if (isNil(player)) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    const updatedProfile = await this.playerTeamProfileService.changeStatus({
      ...dto,
      modifierId,
      status: dto.status,
    });

    if (isError(updatedProfile)) {
      updatedProfile.addContext({
        service: PlayerService.name,
        method: this.changeStatus.name,
        operation: this.playerTeamProfileService.changeStatus.name,
        playerId: dto.playerId,
        coachUserId: modifierId,
      });

      return updatedProfile;
    }

    if (
      dto.startRegistrationProcess &&
      updatedProfile.status === PlayerTeamStatusWithTeam.RegistrationPending
    ) {
      const team = await this.teamsService.findById(dto.teamId);

      if (isError(team)) {
        team.addContext({
          service: PlayerService.name,
          method: this.changeStatus.name,
          operation: this.teamsService.findById.name,
          teamId: dto.teamId,
        });

        return team;
      }

      if (!team || !team.fee || !team.currency) {
        return new DomainError(ErrorMessages.InvalidAction);
      }

      const paymentRequest = PaymentRequest.Entity.create({
        status: PaymentRequest.PaymentStatus.Pending,
        playerId: player.id,
        amount: team.fee,
        currency: team.currency,
        reason: PaymentRequest.PaymentReason.Subscription,
        organizationId: dto.organizationId,
        teamId: dto.teamId,
        playerTeamProfileId: updatedProfile.id,
        stripePriceId: team.stripePriceId,
        stripeProductId: team.stripeProductId,
      });

      const result = await this.paymentRequestsService.createPlayerPayment(
        player.id,
        paymentRequest,
      );

      if (isError(result)) {
        result.addContext({
          service: PlayerService.name,
          method: this.changeStatus.name,
          operation: this.paymentRequestsService.createPlayerPayment.name,
          playerId: player.id,
          paymentRequest,
        });

        return result;
      }
    }

    return Player.toPopulatedPlayer(player, [updatedProfile]);
  }

  async hardRemoveFromTeam(
    playerTeamProfileId: PlayerTeamProfileId,
    modifierId: ProfileId,
    playerId: PlayerId,
  ): Promise<UnexpectedError | DomainError | ParsingError | PopulatedPlayer> {
    const removeOperation = await this.playerTeamProfileService.removeFromTeam({
      playersTeamProfilesIds: [playerTeamProfileId],
      modifierId,
    });

    if (isError(removeOperation)) {
      removeOperation.addContext({
        service: PlayerService.name,
        method: this.hardRemoveFromTeam.name,
        operation: this.playerTeamProfileService.removeFromTeam.name,
        playerId,
        coachUserId: modifierId,
      });

      return removeOperation;
    }

    const updatedPlayer = await this.repo.getPopulatedPlayer(playerId);

    if (isError(updatedPlayer)) {
      updatedPlayer.addContext({
        service: PlayerService.name,
        method: this.hardRemoveFromTeam.name,
        operation: this.repo.getPopulatedPlayer.name,
        playerId,
        coachUserId: modifierId,
      });

      return updatedPlayer;
    }

    return updatedPlayer;
  }

  async removeFromOrganization(organizationId: OrganizationId, playerId: PlayerId) {
    return this.playerTeamProfileService.updateToRemovedFromOrganization({
      organizationId,
      playerId,
    });
  }

  async searchPlayers(
    params: PlayersSearchParams,
  ): Promise<UnexpectedError | ParsingError | Pagination.PaginatedData<PopulatedPlayer>> {
    const players = await this.repo.searchPlayers(params);

    if (isError(players)) {
      players.addContext({
        service: PlayerService.name,
        method: this.searchPlayers.name,
        operation: this.repo.searchPlayers.name,
        params,
      });

      return players;
    }

    return players;
  }

  async searchTeamPlayers(
    teamId: TeamId,
    params: PlayersSearchParams,
  ): Promise<UnexpectedError | ParsingError | PopulatedPlayer[]> {
    const paginatedPlayers = await this.repo.searchPlayers({
      ...params,
      query: {
        ...params.query,
        teamId,
        playerStatusNot: [PlayerTeamStatus.RemovedTeam],
        // override the pagination property to return "all" (10k) results
        pagination: { limit: 10000, skip: 0 },
      },
    });

    if (isError(paginatedPlayers)) {
      paginatedPlayers.addContext({
        service: PlayerService.name,
        method: this.searchTeamPlayers.name,
        operation: this.repo.searchPlayers.name,
        teamId,
      });

      return paginatedPlayers;
    }

    return paginatedPlayers.data;
  }

  async getReviewPlayers(
    teamId: TeamId,
    ids: PlayerId[],
  ): Promise<UnexpectedError | ParsingError | Player[]> {
    /*
    Querying players by only teamId is not enough, because some players might
    have a historic review but not be part of the team anymore. Therefore we
    also query by existingReview.playerId for each existing review.
    */
    const players = await this.repo.getPlayersByTeamAndIds(teamId, ids);

    if (isError(players)) {
      players.addContext({
        service: PlayerService.name,
        method: this.getReviewPlayers.name,
        operation: this.repo.getPlayersByTeamAndIds.name,
        params: { teamId, ids },
      });

      return players;
    }

    return players;
  }

  async playerOrGuardianExists(email: Email) {
    const result = await this.repo.findRelatedPlayers(email);

    if (isError(result)) {
      result.addContext({
        service: PlayerService.name,
        method: this.playerOrGuardianExists.name,
        operation: this.repo.findRelatedPlayers.name,
        email,
      });

      return result;
    }

    return result.length > 0;
  }

  async findRelatedPlayers(email: Email): Promise<Player[] | UnexpectedError | ParsingError> {
    const result = await this.repo.findRelatedPlayers(email);

    if (isError(result)) {
      result.addContext({
        service: PlayerService.name,
        method: this.findRelatedPlayers.name,
        operation: this.repo.findRelatedPlayers.name,
        email,
      });

      return result;
    }

    return result;
  }

  async findExistingPlayer(dto: GetPlayerDto) {
    if (Player.hasEmailIdentification(dto)) {
      return this.repo.findByEmail(dto.email);
    }

    return this.repo.findByGuardian(dto);
  }

  async isWithinOrganization(
    id: PlayerId,
    organizationId: OrganizationId,
  ): Promise<boolean | ParsingError | UnexpectedError | DomainError> {
    const player = await this.repo.getPopulatedPlayer(id);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });

      return player;
    }

    if (!player) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return Player.isPartOfOrganization(player, organizationId);
  }

  async getById(id: PlayerId) {
    const player = await this.repo.getById(id);

    if (isError(player)) {
      player.addContext({
        service: PlayerService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });

      return player;
    }

    if (!player) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return player;
  }

  async demoCreateApplicant(organizationId: OrganizationId) {
    const player = Player.toDemoInstance();

    const result = await this.createApplicant({ ...player, organizationId });

    if (isError(result)) {
      result.addContext({
        service: PlayerService.name,
        method: this.demoCreateApplicant.name,
        operation: this.createApplicant.name,
        playerId: player.id,
      });

      return result;
    }

    return result;
  }

  async demoCreateApplicants(organizationId: OrganizationId, numberOfPlayers: PositiveInteger) {
    const players = [];

    for (let index = 0; index < numberOfPlayers; index++) {
      const player = await this.demoCreateApplicant(organizationId);

      if (isError(player)) {
        player.addContext({
          service: PlayerService.name,
          method: this.demoCreateApplicants.name,
          operation: this.demoCreateApplicant.name,
        });

        return player;
      }

      players.push(player);
    }

    return players;
  }
}
