import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Primitive, CreatePlayerDto } from "@mio/helpers";

/* Primitive doesn't work automatically with nested objects thus the below workaround*/
export type DataShape = Partial<
  Omit<Primitive<CreatePlayerDto>, "guardian" | "address"> & {
    guardian?: Partial<Primitive<PlayerGuardian>>;
    address: Primitive<PlayerAddress>;
  }
>;
