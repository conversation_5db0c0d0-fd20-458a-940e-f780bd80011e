import { FC } from "react";
import { useDebounce } from "react-use";
import { useTranslation } from "react-i18next";

import { OrganizationId, Player, isError } from "@mio/helpers";
import { Box, Alert, CircularProgress, playersState, Button, useOrganization } from "@mio/ui";

import { DataShape } from "./types";
import { useInlineLogin } from "./inlineLogin";

/* checks whether the player has already registered for this organization */
export const usePlayerEligibility = (data: DataShape, organizationId: OrganizationId) => {
  const { t } = useTranslation();
  const findPlayer = playersState.useFindPlayer(organizationId);
  const { mutate } = findPlayer;
  const { openInlineLogin, InlineLoginDialog } = useInlineLogin();

  useDebounce(
    () => {
      const parsed = Player.parseGetPlayerDto({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        guardianEmail: data.guardian?.email,
      });

      if (!isError(parsed)) {
        mutate(parsed);
      }
    },
    1000,
    [data.email, data.firstName, data.lastName, data.guardian?.email, mutate],
  );

  const isChecking = findPlayer.isLoading;
  const alreadyRegistered = findPlayer.isSuccess && findPlayer.data;

  type Props = {
    identificationType: "email" | "guardian";
  };

  console.log("findPlayer", findPlayer);

  const EligibilityIndicator: FC<Props> = ({ identificationType }) => {
    if (isChecking) {
      return (
        <Box>
          <Alert severity="info">{t("eligibility.checking")}</Alert>
          <CircularProgress />
        </Box>
      );
    }

    if (alreadyRegistered && data.email && identificationType === "email") {
      return (
        <Alert severity="warning">
          <span>{t("eligibility.email-exists")}</span>
          <Button sx={{ ml: 2 }} onClick={() => openInlineLogin(data.email)}>
            {t("eligibility.login-button")}
          </Button>
          {InlineLoginDialog}
        </Alert>
      );
    }

    if (alreadyRegistered && !data.email && identificationType === "guardian") {
      return (
        <Alert severity="warning">
          <span>{t("eligibility.guardian-exists")}</span>
          <Button sx={{ ml: 2 }} onClick={() => openInlineLogin(data.guardian?.email)}>
            {t("eligibility.login-button")}
          </Button>
          {InlineLoginDialog}
        </Alert>
      );
    }

    return null;
  };

  return { EligibilityIndicator, isChecking };
};
