import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Primitive, UpdatePlayerDto } from "@mio/helpers";

/* Primitive doesn't work automatically with nested objects thus the below workaround*/
export type DataShape = Partial<
  Omit<Primitive<UpdatePlayerDto>, "guardian" | "address"> & {
    guardian?: Partial<Primitive<PlayerGuardian>>;
    address: Primitive<PlayerAddress>;
  }
>;
