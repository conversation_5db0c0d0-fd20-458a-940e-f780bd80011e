export enum UrlParams {
  RecurringTeamEventId = "recurringTeamEventId",
  TeamEventId = "teamEventId",
  OrganizationId = "organizationId",
  OrganizationSlug = "organizationSlug",
  InviteId = "inviteId",
  TeamId = "teamId",
  SeasonId = "seasonId",
  PlayerTeamProfileId = "playerTeamProfileId",
  PlayerId = "playerId",
  PlayerImageId = "playerImageId",
  CoachId = "coachId",
  RoleId = "roleId",
  PermissionId = "permissionId",
  FinancialIntegrationId = "financialIntegrationId",
  EmailTemplateId = "emailTemplateId",
  SeasonPlanId = "seasonPlanId",

  TrainingSessionPracticeId = "trainingSessionPracticeId",
  TrainingSessionPlanId = "trainingSessionPlanId",
  TrainingSessionReviewId = "trainingSessionReviewId",
  TrainingSessionPlayerReviewId = "trainingSessionPlayerReviewId",

  FootballMatchPlanId = "footballMatchPlanId",
  FootballMatchReviewId = "footballMatchReviewId",
  FootballMatchAttemptForId = "footballMatchAttemptForId",
  FootballMatchAttemptAgainstId = "footballMatchAttemptAgainstId",

  PaymentRequestId = "paymentRequestId",
}

export enum UrlQuery {
  dobBefore = "dobBefore",
  dobAfter = "dobAfter",
  playerStatus = "playerStatus",
  teamId = "teamId",
  startDate = "startDate",
  endDate = "endDate",
  code = "code",
  footballMatchAttemptForType = "footballMatchAttemptForType",
}

const organizationRoot = `organizations/:${UrlParams.OrganizationId}` as const;

export const apiUrls = {
  registerWithInvite: "auth/register",
  login: "auth/login",
  playerCode: "auth/players/code",
  playerLogin: "auth/players/login",

  assets: {
    player_images: `assets/players/:${UrlParams.PlayerId}/images`,
    player_image: `assets/players/:${UrlParams.PlayerId}/images/:${UrlParams.PlayerImageId}`,

    coach_images: `assets/organizations/:${UrlParams.OrganizationId}/coaches/:${UrlParams.CoachId}/images`,
  },
  player_documents: {
    photo: `assets/players/:${UrlParams.PlayerId}/photo/:${UrlParams.PlayerImageId}`,
    document_image: `assets/players/:${UrlParams.PlayerId}/document-images/:${UrlParams.PlayerImageId}`,
    document_images: `assets/players/:${UrlParams.PlayerId}/document-images`,
    submit: `assets/players/:${UrlParams.PlayerId}/documents/submit`,
  },

  payments: {
    my_payments: `payments/players/:${UrlParams.PlayerId}`,
    create_checkout_session: `payments/:${UrlParams.PaymentRequestId}/create-checkout-session`,
  },

  currentPlayerUser: "player-users/me",
  currentPlayers: "players/me",

  passwordResetInitiation: "users/password/reset-request",
  passwordResetAction: "users/password/reset",
  currentUser: "users/me",
  organizationUsers: `organizations/:${UrlParams.OrganizationId}/users`,
  updateMemberPermissions: `organizations/:${UrlParams.OrganizationId}/permissions`,
  updateMemberStatus: `organizations/:${UrlParams.OrganizationId}/permissions/status`,

  roles: `${organizationRoot}/roles`,
  role: `${organizationRoot}/roles/:${UrlParams.RoleId}`,

  ownerPermissions: `${organizationRoot}/permissions/owner`,
  permissions: `${organizationRoot}/permissions`,
  permission: `${organizationRoot}/permissions/:${UrlParams.PermissionId}`,
  currentPermissions: `organizations/:${UrlParams.OrganizationId}/permissions/me`,

  currentProfile: "profiles/me",
  teamCoaches: `${organizationRoot}/teams/:${UrlParams.TeamId}/coaches`,

  createOrganization: "organizations",
  updateOrganization: `organizations/:${UrlParams.OrganizationId}`,
  getOrganization: `organizations/:${UrlParams.OrganizationId}`,
  getOrganizationBySlug: `organizations/slug/:${UrlParams.OrganizationSlug}`,
  joinOrganization: `join/invites/:${UrlParams.InviteId}`,

  createSystemInvite: `${organizationRoot}/system-invites`,
  createInvite: `${organizationRoot}/invites`,
  getInvite: `invites/:${UrlParams.InviteId}`,
  getInvites: `${organizationRoot}/invites`,
  inviteDetails: `${organizationRoot}/invites/:${UrlParams.InviteId}`,

  createSeason: `organizations/:${UrlParams.OrganizationId}/seasons`,
  findSeasons: `organizations/:${UrlParams.OrganizationId}/seasons`,
  updateSeason: `organizations/:${UrlParams.OrganizationId}/seasons/:${UrlParams.SeasonId}`,
  getSeason: `organizations/:${UrlParams.OrganizationId}/seasons/:${UrlParams.SeasonId}`,
  currentSeason: `organizations/:${UrlParams.OrganizationId}/current-season`,

  seasonPlan: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/season-plan`,
  seasonPlans: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/season-plans`,
  trainingSessionPractice: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-practice`,
  trainingSessionPractices: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-practices`,
  trainingSessionPlans: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-plans`,
  trainingSessionPlan: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-plan`,
  trainingSessionReviews: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-reviews`,
  trainingSessionReview: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-reviews/:${UrlParams.TrainingSessionReviewId}`,
  trainingSessionPlayerReviews: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-player-reviews`,
  trainingSessionPlayerReview: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/training-session-player-reviews/:${UrlParams.TrainingSessionPlayerReviewId}`,

  footballMatchPlans: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-plans`,
  footballMatchPlan: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-plan/:${UrlParams.FootballMatchPlanId}`,

  footballMatchReviews: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-reviews`,
  footballMatchReview: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-review/:${UrlParams.FootballMatchPlanId}`,

  footballMatchPlayersReviews: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-players-reviews/:${UrlParams.FootballMatchPlanId}`,
  footballMatchPlayersReview: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-players-review/:${UrlParams.FootballMatchPlanId}`,
  footballMatchPlayersAverageReviews: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-players-average-review`,

  footballMatchAttemptFor: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-attempt-for/:${UrlParams.FootballMatchAttemptForId}`,
  footballMatchAttemptsFor: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-attempts-for/:${UrlParams.FootballMatchPlanId}`,
  footballMatchAttemptsForExtra: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-attempt-for-extra/:${UrlParams.FootballMatchPlanId}`,

  footballMatchAttemptAgainst: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-attempt-against/:${UrlParams.FootballMatchAttemptAgainstId}`,
  footballMatchAttemptsAgainst: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-attempts-against/:${UrlParams.FootballMatchPlanId}`,
  footballMatchAttemptsAgainstExtra: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match-attempt-against-extra/:${UrlParams.FootballMatchPlanId}`,

  footballMatchPlayerStats: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/football-match/player-stats`,
  statsTeamAttemptsFor: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/stats/goals-scored`,
  statsTeamAttemptsAgainst: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/stats/goals-conceded`,

  generalFinancialIntegration: `organizations/:${UrlParams.OrganizationId}/financial-integration`,
  identifiedFinancialIntegration: `organizations/:${UrlParams.OrganizationId}/financial-integration/:${UrlParams.FinancialIntegrationId}`,

  generalGoCardlessSubscriptions: `organizations/:${UrlParams.OrganizationId}/go-cardless/subscriptions`,
  generalGoCardlessPayments: `organizations/:${UrlParams.OrganizationId}/go-cardless/payments`,

  stripeSubscriptions: `organizations/:${UrlParams.OrganizationId}/stripe/subscriptions`,

  recurringTeamEvents: `${organizationRoot}/teams/:${UrlParams.TeamId}/recurring-events`,
  recurringTeamEvent: `${organizationRoot}/teams/:${UrlParams.TeamId}/recurring-events/:${UrlParams.RecurringTeamEventId}`,

  teamEvents: `${organizationRoot}/teams/:${UrlParams.TeamId}/events`,
  teamEvent: `${organizationRoot}/teams/:${UrlParams.TeamId}/events/:${UrlParams.TeamEventId}`,
  playerEvents: `players/:${UrlParams.PlayerId}/events`,
  playerTeamEvent: `events/:${UrlParams.TeamEventId}/players/:${UrlParams.PlayerId}`,

  createTeam: `organizations/:${UrlParams.OrganizationId}/teams`,
  getTeam: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}`,
  updateTeam: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}`,
  deleteTeam: `teams/:${UrlParams.TeamId}`,
  getTeams: `teams/organization/:${UrlParams.OrganizationId}`,
  getAdminTeams: `admin/teams/organization/:${UrlParams.OrganizationId}`,

  player: `players/${UrlParams.PlayerId}`,
  createApplicant: "players/applicant",
  applyToOrganization: `organizations/:${UrlParams.OrganizationId}/players/:${UrlParams.PlayerId}/apply`,
  getTeamPlayers: `organizations/:${UrlParams.OrganizationId}/players/:${UrlParams.TeamId}`,
  searchOrganizationPlayers: `organizations/:${UrlParams.OrganizationId}/players`,
  findPlayer: `organizations/:${UrlParams.OrganizationId}/players/find`,
  isPlayerInOrganization: `organizations/:${UrlParams.OrganizationId}/players/:${UrlParams.PlayerId}/exists`,
  assignNewTeam: `organizations/:${UrlParams.OrganizationId}/players/assign/team`,
  changeTeamStatus: `organizations/:${UrlParams.OrganizationId}/players/status`,
  getPlayers: `players/:${UrlParams.TeamId}`,
  removeFromTeam: `organizations/:${UrlParams.OrganizationId}/teams/:${UrlParams.TeamId}/players/:${UrlParams.PlayerId}`,
  removeFromOrganization: `organizations/:${UrlParams.OrganizationId}/players/:${UrlParams.PlayerId}/remove-from-organization`,

  sendEmail: `organizations/:${UrlParams.OrganizationId}/email`,
  sendMarketingEmail: `marketing/email`,

  emailTemplates: `organizations/:${UrlParams.OrganizationId}/email-templates`,
  emailTemplate: `organizations/:${UrlParams.OrganizationId}/email-templates/:${UrlParams.EmailTemplateId}`,

  healthCheck: "health-check",

  demoTeam: "demo-team",
  demoPlayer: "demo-player",
  demoOrganization: "demo-organization",
  demoCompleteOrganization: "demo-complete-organization",
  demoCompleteTeam: "demo-complete-team",
  demoTeamPlayers: "demo-team-players",
} as const;

type ExtractRouteParams<T extends string> = T extends `${infer Start}:${infer Param}/${infer Rest}`
  ? Param | ExtractRouteParams<Rest>
  : T extends `${infer _Start}:${infer Param}`
  ? Param
  : never;

type RouteParameters<T extends string> = {
  [K in ExtractRouteParams<T>]: string | number;
};

/* const url = '/users/:userId'
   const formatted = buildUrlWithParams(url, { userId: '12345'});
   // formatted = '/users/12345'
*/
export const buildUrlWithParams = <T extends string>(url: T, params: RouteParameters<T>) => {
  return url
    .split("/")
    .map((elem) => {
      if (elem.startsWith(":")) {
        const param = elem.substring(1);
        return String(params[param as keyof RouteParameters<T>]);
      }
      return elem;
    })
    .join("/");
};

export const sharedUrls = {
  passwordReset: `auth/password-reset/update`,
  playerLoginWithCode: "/login",
} as const;
