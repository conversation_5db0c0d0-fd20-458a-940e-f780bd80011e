import { ZodError } from "zod";

import { isObjectLike, GenericRecord } from "./GenericRecord";
import { UnknownRecord } from "./utilities";

/* Differentiate between error types since class information will be lost
during the BE-FE http communication */
export enum ErrorTypes {
  ParsingError = "ParsingError",
  UnexpectedError = "UnexpectedError",
  DomainError = "DomainError",
}

/* Those could be translatable keys for the FE*/
export enum ErrorMessages {
  InvalidFields = "InvalidFields",
  InvalidCredentials = "InvalidCredentials",
  InvalidParam = "InvalidParam",
  InvalidQueryParam = "InvalidQueryParam",
  UnexpectedError = "UnexpectedError",
  EntityAlreadyExists = "EntityAlreadyExists",
  MatchAlreadyExistsInGameWeek = "MatchAlreadyExistsInGameWeek",
  EntityNotFound = "EntityNotFound",
  MultipleEntitiesFound = "MultipleEntitiesFound",
  InvalidUser = "InvalidUser",
  UserNotFound = "UserNotFound",
  SDKUninitialized = "SDKUninitialized",
  InviteAlreadyRedeemed = "InviteAlreadyRedeemed",
  InviteExpired = "InviteExpired",
  InvalidInvite = "InvalidInvite",
  RepositoryError = "RepositoryError",
  OrganizationNotFound = "OrganizationNotFound",
  TeamNotFound = "TeamNotFound",
  ProfileNotFound = "ProfileNotFound",
  InvalidPlayerTeamProfile = "InvalidPlayerTeamProfile",
  InvalidPlayer = "InvalidPlayer",
  MissingPermissions = "MissingPermissions",
  PermissionDenied = "PermissionDenied",
  InvalidAction = "InvalidAction",
  ActionExpired = "ActionExpired",
  Conflict = "Conflict",
  SeasonNotFound = "SeasonNotFound",
  InvalidFile = "InvalidFile",
  FileTooLarge = "FileTooLarge",
  Unauthorized = "Unauthorized",
  BadRequest = "BadRequest",
  EntityNotDeletable = "EntityNotDeletable",
}

/* Predictable error shapes for the FE */
export interface APIError {
  type: ErrorTypes;
  message: ErrorMessages;
  errors?: GenericRecord;
  originalError?: unknown;
}

const customZodErrorParser = (errors: ZodError) => {
  const result = {} as GenericRecord;

  errors.errors.forEach((error) => {
    if (error.code === "invalid_union") {
      const unionErrors = error.unionErrors;

      unionErrors.forEach((unionError) => {
        unionError.errors.forEach((innerError) => {
          const innerPath = innerError.path.join(".");
          if (innerPath) {
            const existingValues = (result[innerPath] as string[]) || [];
            result[innerPath] = [...existingValues, innerError.message] as string[];
          }
        });
      });
    }
    const path = error.path.join(".");
    if (path) {
      result[path] = [error.message];
    }
  });

  return result;
};

type ErrorContext = string | GenericRecord;

abstract class ErrorWithContext extends Error {
  readonly context: ErrorContext[] = [];

  constructor(context?: ErrorContext) {
    super();

    if (context) {
      this.addContext(context);
    }
  }

  addContext(...contexts: ErrorContext[]) {
    contexts.forEach((ctx) => this.context.push(ctx));
  }
}

export class ParsingError<ErrorShape = GenericRecord> extends ErrorWithContext {
  readonly type = ErrorTypes.ParsingError;
  readonly message: ErrorMessages = ErrorMessages.InvalidFields;
  /* ParsingError.errors is any object by default, but we can make it more 
  specific if needed by passing the ErrorShape generic type parameter. Being
  more specific is useful in certain cases, e.g. - FE validation when creating
  a password, where we need granular information whether the Password passes
  each individual requirement about length, number of digits and so on */
  readonly errors = {} as ErrorShape;

  constructor(errors: ZodError | UnknownRecord, context?: ErrorContext) {
    super(context);

    if (errors instanceof ZodError) {
      // this.errors = errors.flatten().fieldErrors;
      /* https://giters.com/colinhacks/zod/issues/792 zod has problems with
     discriminated union types errors so we need this fix for now */
      this.errors = customZodErrorParser(errors) as ErrorShape;
    } else {
      this.errors = errors as ErrorShape;
    }
  }
}
export class DomainError extends ErrorWithContext implements APIError {
  readonly type = ErrorTypes.DomainError;

  constructor(public message: ErrorMessages, context?: ErrorContext) {
    super(context);
  }
}

export class UnexpectedError extends ErrorWithContext implements APIError {
  readonly type = ErrorTypes.UnexpectedError;
  readonly message = ErrorMessages.UnexpectedError;

  constructor(public originalError?: unknown, context?: ErrorContext) {
    super(context);
  }
}

export type CustomError = UnexpectedError | DomainError | ParsingError;

export const isError = (error: unknown): error is CustomError =>
  error instanceof UnexpectedError || error instanceof DomainError || error instanceof ParsingError;

export const isKnownApiError = (error: unknown): error is APIError =>
  isObjectLike(error) && Object.values<unknown>(ErrorTypes).includes(error.type);

export const isCustomError = (error: unknown): error is CustomError =>
  isObjectLike(error) && Object.values<unknown>(ErrorTypes).includes(error.type);

export const isParsingError = (error: unknown): error is ParsingError => {
  return isCustomError(error) && error.type === ErrorTypes.ParsingError;
};
