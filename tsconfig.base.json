{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@mio/helpers": ["libs/helpers/src/index.ts"], "@mio/ui": ["libs/ui/src/index.ts"], "@mio/ui/lib/*": ["libs/ui/src/lib/*"]}, "strict": true}, "exclude": ["node_modules", "tmp"]}